#!/usr/bin/env python3
"""
系统启动流程精确诊断脚本
专门测试交易系统初始化过程中的全局交易所实例设置问题

目标：
1. 模拟真实的系统启动流程
2. 精确定位全局交易所实例丢失的时机
3. 验证WebSocket启动失败对全局实例的影响
4. 测试在不同启动条件下的全局实例状态
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def print_section(title):
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

async def main():
    print("🚀 系统启动流程精确诊断开始...")
    
    diagnosis_result = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "startup_phases": {},
        "global_exchanges_status": {},
        "issues_found": [],
        "recommendations": []
    }
    
    # 1. 测试基础导入和初始化器创建
    print_section("1. 基础组件导入测试")
    
    try:
        from core.trading_system_initializer import (
            get_trading_system_initializer, 
            get_global_exchanges, 
            set_global_exchanges
        )
        print("✅ 核心模块导入成功")
        
        # 创建初始化器
        initializer = get_trading_system_initializer()
        print(f"✅ 交易系统初始化器创建成功: {type(initializer).__name__}")
        
        # 检查初始状态
        initial_global = get_global_exchanges()
        print(f"📊 初始全局交易所状态: {initial_global}")
        diagnosis_result["global_exchanges_status"]["initial"] = str(initial_global)
        
        diagnosis_result["startup_phases"]["basic_import"] = "SUCCESS"
        
    except Exception as e:
        print(f"❌ 基础组件导入失败: {e}")
        diagnosis_result["startup_phases"]["basic_import"] = f"FAILED: {e}"
        diagnosis_result["issues_found"].append(f"基础组件导入异常: {e}")
        return diagnosis_result
    
    # 2. 测试交易所初始化阶段
    print_section("2. 交易所初始化阶段测试")
    
    try:
        # 检查环境变量
        required_keys = [
            'GATE_API_KEY', 'GATE_API_SECRET', 
            'BYBIT_API_KEY', 'BYBIT_API_SECRET', 
            'OKX_API_KEY', 'OKX_API_SECRET', 'OKX_API_PASSPHRASE'
        ]
        missing_keys = [key for key in required_keys if not os.getenv(key)]
        
        if missing_keys:
            print(f"⚠️ 缺少API密钥: {missing_keys}")
            diagnosis_result["startup_phases"]["exchange_init"] = f"SKIPPED: Missing keys {missing_keys}"
        else:
            print("✅ API密钥配置完整")
            
            # 调用交易所初始化
            print("🔄 开始交易所初始化...")
            exchanges_result = await initializer.initialize_exchanges()
            print(f"📊 交易所初始化结果: {exchanges_result}")
            
            # 检查初始化器的exchanges属性
            if hasattr(initializer, 'exchanges') and initializer.exchanges:
                print(f"✅ 初始化器exchanges属性: {list(initializer.exchanges.keys())}")
                diagnosis_result["startup_phases"]["exchange_init"] = "SUCCESS"
                
                # 检查全局实例状态
                after_exchange_init = get_global_exchanges()
                print(f"📊 交易所初始化后全局状态: {after_exchange_init}")
                diagnosis_result["global_exchanges_status"]["after_exchange_init"] = str(after_exchange_init)
                
            else:
                print("❌ 初始化器exchanges属性为空")
                diagnosis_result["startup_phases"]["exchange_init"] = "FAILED: No exchanges"
                diagnosis_result["issues_found"].append("交易所初始化失败")
                
    except Exception as e:
        print(f"❌ 交易所初始化异常: {e}")
        diagnosis_result["startup_phases"]["exchange_init"] = f"ERROR: {e}"
        diagnosis_result["issues_found"].append(f"交易所初始化异常: {e}")
    
    # 3. 测试全局实例设置阶段
    print_section("3. 全局实例设置阶段测试")
    
    try:
        if hasattr(initializer, 'exchanges') and initializer.exchanges:
            print("🔄 手动调用set_global_exchanges...")
            set_global_exchanges(initializer.exchanges)
            
            # 验证设置结果
            after_manual_set = get_global_exchanges()
            print(f"✅ 手动设置后全局状态: {list(after_manual_set.keys()) if after_manual_set else None}")
            diagnosis_result["global_exchanges_status"]["after_manual_set"] = str(after_manual_set)
            diagnosis_result["startup_phases"]["global_set"] = "SUCCESS"
            
        else:
            print("❌ 无法设置全局实例：exchanges为空")
            diagnosis_result["startup_phases"]["global_set"] = "FAILED: No exchanges to set"
            
    except Exception as e:
        print(f"❌ 全局实例设置异常: {e}")
        diagnosis_result["startup_phases"]["global_set"] = f"ERROR: {e}"
        diagnosis_result["issues_found"].append(f"全局实例设置异常: {e}")
    
    # 4. 测试完整系统初始化（包含WebSocket）
    print_section("4. 完整系统初始化测试")
    
    try:
        print("🔄 调用完整系统初始化...")
        
        # 记录初始化前的全局状态
        before_full_init = get_global_exchanges()
        print(f"📊 完整初始化前全局状态: {list(before_full_init.keys()) if before_full_init else None}")
        diagnosis_result["global_exchanges_status"]["before_full_init"] = str(before_full_init)
        
        # 调用完整初始化
        success = await initializer.initialize_all_systems()
        print(f"📊 完整系统初始化结果: {success}")
        
        # 记录初始化后的全局状态
        after_full_init = get_global_exchanges()
        print(f"📊 完整初始化后全局状态: {list(after_full_init.keys()) if after_full_init else None}")
        diagnosis_result["global_exchanges_status"]["after_full_init"] = str(after_full_init)
        
        if success:
            diagnosis_result["startup_phases"]["full_init"] = "SUCCESS"
            print("✅ 完整系统初始化成功")
        else:
            diagnosis_result["startup_phases"]["full_init"] = "FAILED: Returned False"
            print("❌ 完整系统初始化失败（返回False）")
            
            # 分析失败原因
            if after_full_init and before_full_init:
                print("🔍 分析：全局实例在初始化过程中保持正常")
                diagnosis_result["recommendations"].append("WebSocket启动失败不影响全局交易所实例")
            elif not after_full_init and before_full_init:
                print("🔍 分析：全局实例在完整初始化过程中丢失")
                diagnosis_result["issues_found"].append("完整初始化过程中全局实例丢失")
                diagnosis_result["recommendations"].append("需要检查initialize_all_systems中的全局实例清理逻辑")
            
    except Exception as e:
        print(f"❌ 完整系统初始化异常: {e}")
        diagnosis_result["startup_phases"]["full_init"] = f"ERROR: {e}"
        diagnosis_result["issues_found"].append(f"完整系统初始化异常: {e}")
    
    # 5. 测试交易规则获取在不同阶段的表现
    print_section("5. 交易规则获取测试")
    
    try:
        from core.trading_rules_preloader import TradingRulesPreloader
        preloader = TradingRulesPreloader()
        
        # 测试在当前全局状态下的交易规则获取
        current_global = get_global_exchanges()
        print(f"📊 当前全局状态: {list(current_global.keys()) if current_global else None}")
        
        test_rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
        if test_rule:
            print(f"✅ 交易规则获取成功: 步长={test_rule.get('step_size', 'N/A')}")
            diagnosis_result["startup_phases"]["rule_test"] = "SUCCESS"
        else:
            print("❌ 交易规则获取失败")
            diagnosis_result["startup_phases"]["rule_test"] = "FAILED"
            diagnosis_result["issues_found"].append("交易规则获取失败")
            
    except Exception as e:
        print(f"❌ 交易规则测试异常: {e}")
        diagnosis_result["startup_phases"]["rule_test"] = f"ERROR: {e}"
        diagnosis_result["issues_found"].append(f"交易规则测试异常: {e}")
    
    # 6. 生成诊断报告和建议
    print_section("6. 诊断报告")
    
    # 分析问题模式
    if diagnosis_result["global_exchanges_status"].get("after_exchange_init") != "None":
        diagnosis_result["recommendations"].append("交易所初始化正常，全局实例设置机制工作正常")
    
    if diagnosis_result["startup_phases"].get("full_init", "").startswith("FAILED"):
        diagnosis_result["recommendations"].append("WebSocket启动失败是主要问题，但不影响交易所实例")
        diagnosis_result["recommendations"].append("建议：在main.py中忽略WebSocket启动失败，继续使用交易所实例")
    
    print(f"📊 诊断完成，发现问题: {len(diagnosis_result['issues_found'])}个")
    print(f"📊 生成建议: {len(diagnosis_result['recommendations'])}条")
    
    for issue in diagnosis_result["issues_found"]:
        print(f"   ❌ {issue}")
    
    for rec in diagnosis_result["recommendations"]:
        print(f"   💡 {rec}")
    
    # 保存诊断结果
    os.makedirs("diagnostic_results", exist_ok=True)
    result_file = "diagnostic_results/system_startup_flow_diagnosis.json"
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(diagnosis_result, f, ensure_ascii=False, indent=2)
    
    print(f"📊 详细诊断报告已保存: {result_file}")
    print("🎉 系统启动流程诊断完成")
    
    return diagnosis_result

if __name__ == "__main__":
    asyncio.run(main())
