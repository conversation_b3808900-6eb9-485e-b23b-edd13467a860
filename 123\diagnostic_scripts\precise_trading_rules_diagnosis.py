#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确交易规则获取失败诊断脚本
2025-07-31 创建

根据问题分析.md和日志错误，精确诊断：
1. get_global_exchanges()返回None的根本原因
2. SPK-USDT_gate_spot交易规则获取失败的具体原因
3. _get_precision_from_exchange_api_sync硬编码默认值问题
4. 临时实例创建机制是否正常工作
5. 验证系统初始化流程是否正确调用set_global_exchanges()
"""

import os
import sys
import asyncio
import time
import json
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

async def main():
    """主诊断函数"""
    print("🚀 开始精确交易规则获取失败诊断...")
    
    diagnosis_result = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "issues_found": [],
        "fixes_needed": [],
        "test_results": {}
    }
    
    # 1. 检查全局交易所实例状态
    print_section("1. 检查全局交易所实例状态")
    
    try:
        from core.trading_system_initializer import get_global_exchanges, set_global_exchanges
        
        # 检查当前全局实例状态
        global_exchanges = get_global_exchanges()
        if global_exchanges is None:
            print("❌ get_global_exchanges()返回None")
            print("   这是SPK-USDT_gate_spot交易规则获取失败的根本原因")
            diagnosis_result["issues_found"].append("get_global_exchanges()返回None")
            diagnosis_result["fixes_needed"].append("需要在系统初始化时调用set_global_exchanges()")
            diagnosis_result["test_results"]["global_exchanges"] = "FAILED"
        else:
            print(f"✅ get_global_exchanges()返回{len(global_exchanges)}个交易所")
            print(f"   交易所列表: {list(global_exchanges.keys())}")
            diagnosis_result["test_results"]["global_exchanges"] = "PASSED"
            
    except Exception as e:
        print(f"❌ 检查全局交易所实例失败: {e}")
        diagnosis_result["issues_found"].append(f"全局交易所实例检查异常: {e}")
        diagnosis_result["test_results"]["global_exchanges"] = "ERROR"
    
    # 2. 测试交易规则预加载器
    print_section("2. 测试交易规则预加载器")
    
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        
        preloader = get_trading_rules_preloader()
        print(f"✅ 交易规则预加载器实例: {preloader}")
        
        # 测试获取SPK-USDT_gate_spot交易规则
        rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
        if rule:
            print(f"✅ SPK-USDT_gate_spot交易规则获取成功")
            print(f"   步长: {rule.qty_step}")
            print(f"   最小数量: {rule.min_qty}")
            print(f"   精度: {rule.qty_precision}")
            print(f"   来源: {rule.source}")
            diagnosis_result["test_results"]["spk_usdt_rule"] = "PASSED"
        else:
            print("❌ SPK-USDT_gate_spot交易规则获取失败")
            diagnosis_result["issues_found"].append("SPK-USDT_gate_spot交易规则获取失败")
            diagnosis_result["test_results"]["spk_usdt_rule"] = "FAILED"
            
    except Exception as e:
        print(f"❌ 测试交易规则预加载器失败: {e}")
        diagnosis_result["issues_found"].append(f"交易规则预加载器测试异常: {e}")
        diagnosis_result["test_results"]["spk_usdt_rule"] = "ERROR"
    
    # 3. 测试系统初始化流程
    print_section("3. 测试系统初始化流程")

    try:
        from core.trading_system_initializer import get_trading_system_initializer

        # 创建初始化器实例
        initializer = get_trading_system_initializer()
        print(f"✅ 交易系统初始化器实例: {initializer}")

        # 检查是否有exchanges属性
        if hasattr(initializer, 'exchanges') and initializer.exchanges:
            print(f"✅ 初始化器已有交易所实例: {list(initializer.exchanges.keys())}")

            # 测试设置全局实例
            set_global_exchanges(initializer.exchanges)
            print("✅ 手动设置全局交易所实例成功")

            # 重新测试交易规则获取
            rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
            if rule:
                print(f"✅ 设置全局实例后，SPK-USDT_gate_spot交易规则获取成功")
                diagnosis_result["test_results"]["after_global_set"] = "PASSED"
            else:
                print("❌ 设置全局实例后，交易规则获取仍然失败")
                diagnosis_result["test_results"]["after_global_set"] = "FAILED"

        else:
            print("❌ 初始化器没有交易所实例（这是正常的，因为还没有调用initialize_all_systems）")
            print("🔄 测试完整的系统初始化流程...")

            # 测试完整的初始化流程
            try:
                # 检查环境变量
                import os
                required_keys = ['GATE_API_KEY', 'GATE_API_SECRET', 'BYBIT_API_KEY', 'BYBIT_API_SECRET', 'OKX_API_KEY', 'OKX_API_SECRET', 'OKX_API_PASSPHRASE']
                missing_keys = [key for key in required_keys if not os.getenv(key)]

                if missing_keys:
                    print(f"⚠️ 缺少API密钥，无法测试完整初始化: {missing_keys}")
                    diagnosis_result["test_results"]["full_initialization"] = "SKIPPED"
                else:
                    print("✅ API密钥配置完整，测试完整初始化...")

                    # 调用完整初始化
                    success = await initializer.initialize_all_systems()
                    if success:
                        print("✅ 完整系统初始化成功")

                        # 检查全局实例是否已设置
                        global_exchanges_after = get_global_exchanges()
                        if global_exchanges_after:
                            print(f"✅ 全局交易所实例已正确设置: {list(global_exchanges_after.keys())}")
                            diagnosis_result["test_results"]["full_initialization"] = "PASSED"
                        else:
                            print("❌ 完整初始化后全局实例仍为空")
                            diagnosis_result["test_results"]["full_initialization"] = "FAILED"
                    else:
                        print("❌ 完整系统初始化失败")
                        diagnosis_result["test_results"]["full_initialization"] = "FAILED"

            except Exception as init_error:
                print(f"❌ 完整初始化测试失败: {init_error}")
                diagnosis_result["test_results"]["full_initialization"] = "ERROR"

            diagnosis_result["test_results"]["initializer_exchanges"] = "NORMAL"

    except Exception as e:
        print(f"❌ 测试系统初始化流程失败: {e}")
        diagnosis_result["issues_found"].append(f"系统初始化流程测试异常: {e}")
        diagnosis_result["test_results"]["initializer_exchanges"] = "ERROR"
    
    # 4. 测试临时实例创建机制
    print_section("4. 测试临时实例创建机制")
    
    try:
        # 清空全局实例，测试临时创建
        original_exchanges = get_global_exchanges()
        set_global_exchanges(None)
        print("🔄 已清空全局交易所实例，测试临时实例创建...")
        
        # 尝试获取交易规则（应该触发临时实例创建）
        rule = preloader.get_trading_rule("gate", "SPK-USDT", "spot")
        if rule:
            print(f"✅ 临时实例创建成功，交易规则获取成功")
            print(f"   步长: {rule.qty_step}")
            print(f"   来源: {rule.source}")
            diagnosis_result["test_results"]["temp_instance"] = "PASSED"
        else:
            print("❌ 临时实例创建失败，交易规则获取失败")
            diagnosis_result["issues_found"].append("临时实例创建机制失败")
            diagnosis_result["test_results"]["temp_instance"] = "FAILED"
        
        # 恢复全局实例
        if original_exchanges:
            set_global_exchanges(original_exchanges)
            print("🔄 已恢复原始全局交易所实例")
            
    except Exception as e:
        print(f"❌ 测试临时实例创建失败: {e}")
        diagnosis_result["issues_found"].append(f"临时实例创建测试异常: {e}")
        diagnosis_result["test_results"]["temp_instance"] = "ERROR"
    
    # 5. 检查硬编码默认值问题
    print_section("5. 检查硬编码默认值问题")
    
    try:
        # 直接测试_get_precision_from_exchange_api_sync方法
        from exchanges.gate_exchange import GateExchange
        
        # 创建临时Gate实例
        gate_exchange = GateExchange()
        
        # 测试同步精度获取方法
        precision_info = preloader._get_precision_from_exchange_api_sync(
            gate_exchange, "SPK-USDT", "spot"
        )
        
        if precision_info:
            print(f"✅ 同步精度获取成功")
            print(f"   步长: {precision_info.get('step_size')}")
            print(f"   最小数量: {precision_info.get('min_amount')}")
            print(f"   精度: {precision_info.get('amount_precision')}")
            print(f"   来源: {precision_info.get('source')}")
            
            # 检查是否是硬编码默认值
            if precision_info.get('source') == 'default':
                print("⚠️ 使用的是硬编码默认值，不是真实API数据")
                diagnosis_result["issues_found"].append("使用硬编码默认值而非真实API数据")
                diagnosis_result["fixes_needed"].append("需要实现真实API调用获取精度信息")
            else:
                print("✅ 使用的是真实API数据")
                
            diagnosis_result["test_results"]["precision_sync"] = "PASSED"
        else:
            print("❌ 同步精度获取失败")
            diagnosis_result["issues_found"].append("同步精度获取失败")
            diagnosis_result["test_results"]["precision_sync"] = "FAILED"
            
    except Exception as e:
        print(f"❌ 检查硬编码默认值失败: {e}")
        diagnosis_result["issues_found"].append(f"硬编码默认值检查异常: {e}")
        diagnosis_result["test_results"]["precision_sync"] = "ERROR"
    
    # 6. 生成诊断报告
    print_section("6. 诊断报告")
    
    print("📊 诊断结果汇总:")
    print(f"   发现问题数量: {len(diagnosis_result['issues_found'])}")
    print(f"   需要修复项目: {len(diagnosis_result['fixes_needed'])}")
    
    for issue in diagnosis_result["issues_found"]:
        print(f"   ❌ {issue}")
    
    for fix in diagnosis_result["fixes_needed"]:
        print(f"   🔧 {fix}")
    
    # 保存诊断结果
    result_file = "123/diagnostic_results/precise_trading_rules_diagnosis.json"
    os.makedirs(os.path.dirname(result_file), exist_ok=True)
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(diagnosis_result, f, ensure_ascii=False, indent=2)
    
    print(f"📊 诊断报告已保存: {result_file}")
    print("🎉 精确诊断完成")

if __name__ == "__main__":
    asyncio.run(main())
